# BookSmart

A Chrome extension that uses in-browser AI to intelligently read all open tabs and group them into smart bookmarks.

## Features

- **AI-Powered Tab Analysis**: Uses Chrome's built-in AI API to analyze and understand the content of your open tabs
- **Smart Grouping**: Automatically categorizes and groups related tabs together
- **Rich Metadata**: Captures detailed information about each tab including title, description, visit count, and last accessed time
- **Local Storage**: Saves all bookmarks locally using Chrome's storage API for privacy and speed
- **Simple Interface**: Clean, intuitive popup interface with a single-click bookmarking process

## How It Works

1. Click the "Bookmark All Tabs" button in the extension popup
2. The extension reads all currently open tabs in your browser
3. AI analyzes the content and metadata of each tab
4. Tabs are intelligently grouped and categorized
5. Smart bookmarks are saved to local storage with rich metadata

## Installation

### From Source

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd boosmart
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run build
   ```

4. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `dist` folder

## Development

### Prerequisites

- Node.js and npm
- Chrome browser with AI API support

### Project Structure

```
boosmart/
├── src/
│   ├── background.ts    # Service worker handling main logic
│   └── popup.ts         # Popup interface logic
├── popup.html           # Extension popup UI
├── manifest.json        # Chrome extension manifest
├── tsconfig.json        # TypeScript configuration
├── webpack.config.js    # Build configuration
└── README.md           # This file
```

### Build Scripts

- `npm run build` - Build the extension for production
- `npm run dev` - Build in development mode with source maps
- `npm run watch` - Watch for changes and rebuild automatically

### Technology Stack

- **TypeScript** - Type-safe JavaScript development
- **Webpack** - Module bundling and build process
- **Chrome Extensions API** - Browser integration
- **Chrome AI API** - In-browser AI capabilities

## Permissions

The extension requires the following permissions:

- `tabs` - Access to read open tabs
- `storage` - Local storage for bookmarks
- `history` - Access to browsing history for metadata
- `scripting` - Content script injection
- `ai` - Chrome's built-in AI API
- `<all_urls>` - Access to all websites for content analysis

## Privacy

- All data processing happens locally in your browser
- No data is sent to external servers
- Bookmarks are stored locally using Chrome's storage API
- The extension only accesses tab information when explicitly triggered

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter any issues or have questions, please open an issue on the GitHub repository.

## Roadmap

- [ ] Export bookmarks to various formats
- [ ] Advanced filtering and search capabilities
- [ ] Custom grouping rules
- [ ] Integration with popular bookmark services
- [ ] Enhanced AI categorization options
