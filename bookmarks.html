<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookSmart - Saved Bookmarks</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #007BFF, #00BFFF);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .view-mode-toggle {
            display: flex;
            gap: 0.5rem;
            background: #f8f9fa;
            padding: 0.25rem;
            border-radius: 25px;
            border: 2px solid #e0e0e0;
        }

        .view-mode-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: #666;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .view-mode-btn:hover {
            background: rgba(0, 123, 255, 0.1);
            color: #007BFF;
        }

        .view-mode-btn.active {
            background: #007BFF;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .search-box {
            flex: 1;
            min-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #007BFF;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #007BFF;
            background: white;
            color: #007BFF;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #007BFF;
            color: white;
        }

        .export-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #28a745;
            background: white;
            color: #28a745;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .export-btn:hover {
            background: #28a745;
            color: white;
        }

        .export-btn.secondary {
            border-color: #6c757d;
            color: #6c757d;
        }

        .export-btn.secondary:hover {
            background: #6c757d;
            color: white;
        }

        .reset-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #dc3545;
            background: white;
            color: #dc3545;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .reset-btn:hover {
            background: #dc3545;
            color: white;
        }

        .reset-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: center;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: #666;
        }

        .no-bookmarks {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .no-bookmarks h2 {
            color: #666;
            margin-bottom: 1rem;
        }

        .no-bookmarks p {
            color: #999;
        }

        .bookmark-group {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .group-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .group-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
        }

        .group-count {
            background: #007BFF;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .bookmark-list {
            padding: 0;
        }

        .bookmark-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .bookmark-item:last-child {
            border-bottom: none;
        }

        .bookmark-item:hover {
            background-color: #f8f9fa;
        }

        .bookmark-favicon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
            border-radius: 4px;
            flex-shrink: 0;
        }

        .bookmark-favicon.default {
            background: #007BFF;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .bookmark-content {
            flex: 1;
            min-width: 0;
        }

        .bookmark-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
            cursor: pointer;
            text-decoration: none;
            display: block;
        }

        .bookmark-title:hover {
            color: #007BFF;
        }

        .bookmark-url {
            font-size: 0.9rem;
            color: #666;
            word-break: break-all;
            margin-bottom: 0.25rem;
        }

        .bookmark-description {
            font-size: 0.85rem;
            color: #888;
            line-height: 1.4;
        }

        .bookmark-meta {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
            color: #999;
        }

        .bookmark-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            background: #f8f9fa;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            background: #007BFF;
            color: white;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007BFF;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* Group Navigation Styles */
        .group-navigation {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 2rem;
        }

        .group-navigation-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .group-navigation-header h2 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .group-navigation-header p {
            color: #666;
            font-size: 1rem;
        }

        .group-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .group-card {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .group-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007BFF;
        }

        .group-card-content {
            padding: 1.5rem;
            cursor: pointer;
            flex: 1;
        }

        .group-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .group-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .group-card-count {
            background: #007BFF;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .group-card-stats {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .group-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .group-stat .stat-label {
            color: #666;
            font-weight: 500;
        }

        .group-stat .stat-value {
            color: #333;
            font-weight: 600;
        }

        .group-card-actions {
            display: flex;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: rgba(0, 123, 255, 0.05);
            border-top: 1px solid #e0e0e0;
        }

        .group-action-btn {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }

        .group-action-btn.primary {
            background: #007BFF;
            color: white;
        }

        .group-action-btn.primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .group-action-btn.secondary {
            background: #28a745;
            color: white;
        }

        .group-action-btn.secondary:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        /* Group Detail View Styles */
        .group-detail {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .group-detail-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 2rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .group-detail-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .back-btn {
            background: #007BFF;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #0056b3;
            transform: translateX(-2px);
        }

        .open-all-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .open-all-btn:hover {
            background: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        }

        .group-detail-info h2 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
        }

        .group-detail-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .detail-stat {
            color: #666;
            font-size: 0.95rem;
        }

        .detail-stat strong {
            color: #007BFF;
        }

        .group-detail-bookmarks {
            padding: 0;
        }

        .group-bookmark-item {
            border-bottom: 1px solid #f0f0f0;
        }

        .group-bookmark-item:last-child {
            border-bottom: none;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .close-btn:hover {
            background-color: #f0f0f0;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }

        .copy-json-btn {
            margin-top: 1rem;
            padding: 0.75rem 1.5rem;
            background: #007BFF;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s ease;
        }

        .copy-json-btn:hover {
            background: #0056b3;
        }

        .confirm-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
        }

        .confirm-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            text-align: center;
            position: relative;
        }

        .confirm-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #dc3545;
            margin-bottom: 1rem;
        }

        .confirm-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .confirm-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #856404;
            font-size: 0.95rem;
        }

        .confirm-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .confirm-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .confirm-btn.danger {
            background: #dc3545;
            color: white;
        }

        .confirm-btn.danger:hover {
            background: #c82333;
        }

        .confirm-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .confirm-btn.cancel:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .filter-buttons, .export-buttons {
                justify-content: center;
            }

            .bookmark-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .bookmark-actions {
                margin-left: 0;
                align-self: stretch;
                justify-content: center;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 1rem;
            }

            .view-mode-toggle {
                order: -1;
                width: 100%;
                justify-content: center;
            }

            .group-cards {
                grid-template-columns: 1fr;
            }

            .group-detail-stats {
                flex-direction: column;
                gap: 0.5rem;
            }

            .group-detail-header {
                padding: 1.5rem;
            }

            .group-detail-info h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 BookSmart</h1>
        <p>Your Smart Bookmarks Collection</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="view-mode-toggle">
                <button class="view-mode-btn active" data-mode="sessions">📅 View by Sessions</button>
                <button class="view-mode-btn" data-mode="groups">📁 View by Groups</button>
            </div>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="Search bookmarks...">
            </div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-category="all">All</button>
            </div>
            <div class="export-buttons">
                <button class="export-btn" id="view-json-btn">📄 View JSON</button>
                <button class="export-btn" id="export-json-btn">💾 Export JSON</button>
                <button class="export-btn secondary" id="export-chrome-btn">🌐 Export for Chrome</button>
                <button class="export-btn secondary" id="export-csv-btn">📊 Export CSV</button>
            </div>
            <div class="reset-section">
                <button class="reset-btn" id="reset-bookmarks-btn">🗑️ Reset All Bookmarks</button>
            </div>
        </div>

        <div class="stats" id="stats-container">
            <!-- Stats will be populated by JavaScript -->
        </div>

        <div id="loading" class="loading">
            Loading your bookmarks...
        </div>

        <div id="no-bookmarks" class="no-bookmarks" style="display: none;">
            <h2>No bookmarks found</h2>
            <p>Start by bookmarking some tabs using the extension popup!</p>
        </div>

        <div id="bookmarks-container">
            <!-- Bookmark groups will be populated by JavaScript -->
        </div>
    </div>

    <!-- JSON Viewer Modal -->
    <div id="json-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Raw JSON Data</h2>
                <button class="close-btn" id="close-modal">&times;</button>
            </div>
            <div class="json-viewer" id="json-content">
                <!-- JSON content will be populated by JavaScript -->
            </div>
            <button class="copy-json-btn" id="copy-json-btn">Copy to Clipboard</button>
        </div>
    </div>

    <!-- Reset Confirmation Modal -->
    <div id="confirm-modal" class="confirm-modal">
        <div class="confirm-content">
            <h2 class="confirm-title">⚠️ Reset All Bookmarks</h2>
            <p class="confirm-message">
                Are you sure you want to delete <strong>ALL</strong> your saved bookmarks?
            </p>
            <div class="confirm-warning">
                <strong>Warning:</strong> This action cannot be undone. All bookmark sessions, categories, and the URL index will be permanently deleted.
            </div>
            <div class="confirm-buttons">
                <button class="confirm-btn cancel" id="cancel-reset-btn">Cancel</button>
                <button class="confirm-btn danger" id="confirm-reset-btn">Yes, Delete All</button>
            </div>
        </div>
    </div>

    <script src="js/bookmarks.js"></script>
</body>
</html>
