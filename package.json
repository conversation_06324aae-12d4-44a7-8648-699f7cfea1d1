{"name": "booksmart", "version": "1.0.0", "description": "Uses in-browser AI to read all open tabs and group them into smart bookmarks.", "main": "index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch"}, "keywords": ["chrome-extension", "typescript", "ai"], "author": "", "license": "ISC", "dependencies": {"@mlc-ai/web-llm": "^0.2.46"}, "devDependencies": {"@types/chrome": "^0.0.268", "copy-webpack-plugin": "^12.0.2", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}}