/**
 * bookmarks.ts
 *
 * This script handles the bookmarks viewer page logic.
 * It retrieves saved bookmarks from chrome.storage.local and displays them
 * organized by groups with search and filter functionality.
 */

// Define the structure for our smart bookmark objects (matching background.ts)
interface SmartBookmark {
    url: string;
    title: string;
    description: string;
    lastAccessed: number;
    bookmarkedAt: number;
    visitCount: number; // Historical visit count from browser history
    extensionVisitCount?: number; // New: visits tracked by our extension
    lastExtensionVisit?: number; // New: last visit tracked by our extension
    notes: string;
    favIconUrl?: string;
}

interface BookmarkSession {
    [category: string]: SmartBookmark[];
}

interface AllBookmarks {
    [sessionKey: string]: BookmarkSession;
}

interface GroupedBookmark extends SmartBookmark {
    sessionKey: string;
    sessionDate: string;
}

interface GroupStats {
    name: string;
    bookmarks: GroupedBookmark[];
    totalBookmarks: number;
    totalVisits: number;
    firstVisited: number;
    lastVisited: number;
    firstBookmarked: number;
    lastBookmarked: number;
}

// Global variables
let allBookmarks: AllBookmarks = {};
let filteredBookmarks: AllBookmarks = {};
let currentFilter = 'all';
let currentViewMode: 'sessions' | 'groups' = 'sessions';
let eventListenersSetup = false;

// DOM elements
const loadingDiv = document.getElementById('loading') as HTMLDivElement;
const noBookmarksDiv = document.getElementById('no-bookmarks') as HTMLDivElement;
const bookmarksContainer = document.getElementById('bookmarks-container') as HTMLDivElement;
const searchInput = document.getElementById('search-input') as HTMLInputElement;
const filterButtons = document.querySelector('.filter-buttons') as HTMLDivElement;
const statsContainer = document.getElementById('stats-container') as HTMLDivElement;
const viewJsonBtn = document.getElementById('view-json-btn') as HTMLButtonElement;
const exportJsonBtn = document.getElementById('export-json-btn') as HTMLButtonElement;
const exportChromeBtn = document.getElementById('export-chrome-btn') as HTMLButtonElement;
const exportCsvBtn = document.getElementById('export-csv-btn') as HTMLButtonElement;
const resetBookmarksBtn = document.getElementById('reset-bookmarks-btn') as HTMLButtonElement;
const jsonModal = document.getElementById('json-modal') as HTMLDivElement;
const closeModalBtn = document.getElementById('close-modal') as HTMLButtonElement;
const jsonContent = document.getElementById('json-content') as HTMLDivElement;
const copyJsonBtn = document.getElementById('copy-json-btn') as HTMLButtonElement;
const confirmModal = document.getElementById('confirm-modal') as HTMLDivElement;
const cancelResetBtn = document.getElementById('cancel-reset-btn') as HTMLButtonElement;
const confirmResetBtn = document.getElementById('confirm-reset-btn') as HTMLButtonElement;
const viewModeButtons = document.querySelector('.view-mode-toggle') as HTMLElement;

// Initialize the bookmarks viewer
document.addEventListener('DOMContentLoaded', async () => {
    await loadBookmarks();
    setupEventListeners();
    setupAllEventListeners();
});

async function loadBookmarks() {
    try {
        // Get all stored data from chrome.storage.local
        const result = await chrome.storage.local.get(null);
        
        // Filter for bookmark sessions (keys starting with 'bookmarks_')
        const bookmarkSessions: AllBookmarks = {};
        for (const [key, value] of Object.entries(result)) {
            if (key.startsWith('bookmarks_') && typeof value === 'object') {
                bookmarkSessions[key] = value as BookmarkSession;
            }
        }

        console.log("Raw storage result:", result);
        console.log("Filtered bookmark sessions:", bookmarkSessions);

        // Detailed analysis of each session
        for (const [sessionKey, session] of Object.entries(bookmarkSessions)) {
            console.log(`Session ${sessionKey}:`, session);
            for (const [category, categoryBookmarks] of Object.entries(session)) {
                console.log(`  Category ${category}: ${categoryBookmarks.length} bookmarks`);
                categoryBookmarks.forEach((bookmark, index) => {
                    console.log(`    ${index}: ${bookmark.title} - ${bookmark.url}`);
                });
            }
        }

        // Count total bookmarks
        let totalBookmarksFound = 0;
        for (const session of Object.values(bookmarkSessions)) {
            for (const categoryBookmarks of Object.values(session)) {
                totalBookmarksFound += categoryBookmarks.length;
            }
        }
        console.log(`Total bookmarks found in storage: ${totalBookmarksFound}`);

        allBookmarks = bookmarkSessions;
        filteredBookmarks = { ...allBookmarks };

        loadingDiv.style.display = 'none';

        if (Object.keys(allBookmarks).length === 0) {
            noBookmarksDiv.style.display = 'block';
            return;
        }

        renderStats();
        renderFilterButtons();
        renderCurrentView();

    } catch (error) {
        console.error('Error loading bookmarks:', error);
        loadingDiv.textContent = 'Error loading bookmarks';
    }
}

function renderStats() {
    let totalBookmarks = 0;
    let totalSessions = Object.keys(allBookmarks).length;
    const categories = new Set<string>();

    // Count bookmarks and collect categories
    for (const session of Object.values(allBookmarks)) {
        for (const [category, bookmarks] of Object.entries(session)) {
            categories.add(category);
            totalBookmarks += bookmarks.length;
        }
    }

    const statsHTML = `
        <div class="stat-card">
            <div class="stat-number">${totalBookmarks}</div>
            <div class="stat-label">Total Bookmarks</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${categories.size}</div>
            <div class="stat-label">Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${totalSessions}</div>
            <div class="stat-label">Sessions</div>
        </div>
    `;

    statsContainer.innerHTML = statsHTML;
}

function renderFilterButtons() {
    const categories = new Set<string>();
    
    // Collect all unique categories
    for (const session of Object.values(allBookmarks)) {
        for (const category of Object.keys(session)) {
            categories.add(category);
        }
    }

    // Create filter buttons
    const sortedCategories = Array.from(categories).sort();
    const buttonsHTML = `
        <button class="filter-btn active" data-category="all">All</button>
        ${sortedCategories.map(category => 
            `<button class="filter-btn" data-category="${category}">${category}</button>`
        ).join('')}
    `;

    filterButtons.innerHTML = buttonsHTML;

    // Add event listeners to filter buttons
    filterButtons.addEventListener('click', (e) => {
        const target = e.target as HTMLButtonElement;
        if (target.classList.contains('filter-btn')) {
            // Update active button
            filterButtons.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            target.classList.add('active');
            
            // Update filter
            currentFilter = target.dataset.category || 'all';
            applyFilters();
        }
    });
}

function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', debounce(applyFilters, 300));

    // Export functionality
    viewJsonBtn.addEventListener('click', showJsonModal);
    exportJsonBtn.addEventListener('click', exportAsJson);
    exportChromeBtn.addEventListener('click', exportForChrome);
    exportCsvBtn.addEventListener('click', exportAsCsv);

    // Reset functionality
    resetBookmarksBtn.addEventListener('click', showResetConfirmation);
    cancelResetBtn.addEventListener('click', hideResetConfirmation);
    confirmResetBtn.addEventListener('click', resetAllBookmarks);

    // Modal functionality
    closeModalBtn.addEventListener('click', hideJsonModal);
    copyJsonBtn.addEventListener('click', copyJsonToClipboard);

    // View mode toggle functionality
    viewModeButtons.addEventListener('click', (e) => {
        const target = e.target as HTMLButtonElement;
        if (target.classList.contains('view-mode-btn')) {
            // Update active button
            viewModeButtons.querySelectorAll('.view-mode-btn').forEach(btn => btn.classList.remove('active'));
            target.classList.add('active');

            // Update view mode
            const newMode = target.dataset.mode as 'sessions' | 'groups';
            if (newMode !== currentViewMode) {
                currentViewMode = newMode;
                renderCurrentView();
            }
        }
    });

    // Close modal when clicking outside
    jsonModal.addEventListener('click', (e) => {
        if (e.target === jsonModal) {
            hideJsonModal();
        }
    });

    confirmModal.addEventListener('click', (e) => {
        if (e.target === confirmModal) {
            hideResetConfirmation();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (jsonModal.style.display === 'block') {
                hideJsonModal();
            }
            if (confirmModal.style.display === 'block') {
                hideResetConfirmation();
            }
        }
    });
}

// Setup event listeners for bookmark action buttons
function setupBookmarkActionListeners() {
    // Use event delegation on the bookmarks container for bookmark actions
    bookmarksContainer.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;

        // Handle open in new tab button clicks
        if (target.classList.contains('open-tab-btn')) {
            const url = target.dataset.url;
            if (url) {
                chrome.tabs.create({ url: url });
            }
            return;
        }

        // Handle copy URL button clicks
        if (target.classList.contains('copy-url-btn')) {
            const url = target.dataset.url;
            if (url) {
                navigator.clipboard.writeText(url).then(() => {
                    // Show a brief success message
                    const originalText = target.textContent;
                    target.textContent = '✓';
                    setTimeout(() => {
                        target.textContent = originalText;
                    }, 1000);
                }).catch(err => {
                    console.error('Failed to copy URL:', err);
                });
            }
            return;
        }
    });
}

function applyFilters() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    
    filteredBookmarks = {};

    for (const [sessionKey, session] of Object.entries(allBookmarks)) {
        const filteredSession: BookmarkSession = {};

        for (const [category, bookmarks] of Object.entries(session)) {
            // Apply category filter
            if (currentFilter !== 'all' && category !== currentFilter) {
                continue;
            }

            // Apply search filter
            const filteredBookmarksInCategory = bookmarks.filter(bookmark => {
                if (!searchTerm) return true;
                
                return (
                    bookmark.title.toLowerCase().includes(searchTerm) ||
                    bookmark.url.toLowerCase().includes(searchTerm) ||
                    bookmark.description.toLowerCase().includes(searchTerm)
                );
            });

            if (filteredBookmarksInCategory.length > 0) {
                filteredSession[category] = filteredBookmarksInCategory;
            }
        }

        if (Object.keys(filteredSession).length > 0) {
            filteredBookmarks[sessionKey] = filteredSession;
        }
    }

    renderCurrentView();
}

function renderCurrentView() {
    if (currentViewMode === 'groups') {
        renderGroupNavigation();
        // Hide search and filter controls for group view
        searchInput.parentElement!.style.display = 'none';
        filterButtons.style.display = 'none';
    } else {
        renderBookmarks();
        // Show search and filter controls for session view
        searchInput.parentElement!.style.display = 'block';
        filterButtons.style.display = 'flex';
    }
}

function renderBookmarks() {
    console.log("renderBookmarks called");
    console.log("filteredBookmarks:", filteredBookmarks);
    console.log("Number of filtered sessions:", Object.keys(filteredBookmarks).length);

    if (Object.keys(filteredBookmarks).length === 0) {
        console.log("No filtered bookmarks found, showing no bookmarks message");
        bookmarksContainer.innerHTML = '<div class="no-bookmarks"><h2>No bookmarks match your criteria</h2><p>Try adjusting your search or filter settings.</p></div>';
        return;
    }

    let html = '';

    // Sort sessions by timestamp (newest first)
    const sortedSessions = Object.entries(filteredBookmarks).sort((a, b) => {
        const timestampA = parseInt(a[0].replace('bookmarks_', ''));
        const timestampB = parseInt(b[0].replace('bookmarks_', ''));
        return timestampB - timestampA;
    });

    console.log("Sorted sessions:", sortedSessions);

    for (const [sessionKey, session] of sortedSessions) {
        console.log(`Processing session: ${sessionKey}`, session);
        const sessionDate = new Date(parseInt(sessionKey.replace('bookmarks_', ''))).toLocaleDateString();

        // Sort categories alphabetically
        const sortedCategories = Object.entries(session).sort((a, b) => a[0].localeCompare(b[0]));
        console.log(`Session ${sessionKey} has ${sortedCategories.length} categories:`, sortedCategories.map(([cat, bookmarks]) => `${cat}(${bookmarks.length})`));

        for (const [category, bookmarks] of sortedCategories) {
            console.log(`Rendering category ${category} with ${bookmarks.length} bookmarks`);
            html += `
                <div class="bookmark-group">
                    <div class="group-header">
                        <div>
                            <div class="group-title">${category}</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">Session: ${sessionDate}</div>
                        </div>
                        <div class="group-count">${bookmarks.length}</div>
                    </div>
                    <div class="bookmark-list">
                        ${bookmarks.map(bookmark => renderBookmarkItem(bookmark)).join('')}
                    </div>
                </div>
            `;
        }
    }

    bookmarksContainer.innerHTML = html;
    setupAllEventListeners();
}

function renderBookmarkItem(bookmark: SmartBookmark): string {
    const favicon = bookmark.favIconUrl 
        ? `<img src="${bookmark.favIconUrl}" alt="" class="bookmark-favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`
        : '';
    
    const defaultFavicon = `<div class="bookmark-favicon default" ${bookmark.favIconUrl ? 'style="display:none;"' : ''}>${bookmark.title.charAt(0).toUpperCase()}</div>`;
    
    const lastAccessed = new Date(bookmark.lastAccessed).toLocaleDateString();
    const bookmarkedAt = new Date(bookmark.bookmarkedAt).toLocaleDateString();

    return `
        <div class="bookmark-item">
            ${favicon}
            ${defaultFavicon}
            <div class="bookmark-content">
                <a href="${bookmark.url}" class="bookmark-title" target="_blank" rel="noopener noreferrer">
                    ${escapeHtml(bookmark.title)}
                </a>
                <div class="bookmark-url">${escapeHtml(bookmark.url)}</div>
                ${bookmark.description ? `<div class="bookmark-description">${escapeHtml(bookmark.description)}</div>` : ''}
                <div class="bookmark-meta">
                    <span>Extension Visits: ${bookmark.extensionVisitCount || 0}</span>
                    <span>Historical Visits: ${bookmark.visitCount}</span>
                    <span>Last accessed: ${lastAccessed}</span>
                    <span>Bookmarked: ${bookmarkedAt}</span>
                </div>
            </div>
            <div class="bookmark-actions">
                <button class="action-btn open-tab-btn" data-url="${escapeHtml(bookmark.url)}" title="Open in new tab">
                    🔗
                </button>
                <button class="action-btn copy-url-btn" data-url="${escapeHtml(bookmark.url)}" title="Copy URL">
                    📋
                </button>
            </div>
        </div>
    `;
}

// Utility functions
function escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function debounce(func: Function, wait: number) {
    let timeout: number;
    return function executedFunction(...args: any[]) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait) as any;
    };
}

// Group aggregation functions
function aggregateBookmarksByGroup(bookmarks: AllBookmarks): { [groupName: string]: GroupStats } {
    const groups: { [groupName: string]: GroupStats } = {};

    // Iterate through all sessions and categories
    for (const [sessionKey, session] of Object.entries(bookmarks)) {
        const sessionDate = new Date(parseInt(sessionKey.replace('bookmarks_', ''))).toLocaleDateString();

        for (const [category, categoryBookmarks] of Object.entries(session)) {
            if (!groups[category]) {
                groups[category] = {
                    name: category,
                    bookmarks: [],
                    totalBookmarks: 0,
                    totalVisits: 0,
                    firstVisited: Infinity,
                    lastVisited: 0,
                    firstBookmarked: Infinity,
                    lastBookmarked: 0
                };
            }

            // Add bookmarks to the group
            for (const bookmark of categoryBookmarks) {
                const groupedBookmark: GroupedBookmark = {
                    ...bookmark,
                    sessionKey,
                    sessionDate
                };

                groups[category].bookmarks.push(groupedBookmark);
                groups[category].totalBookmarks++;
                // Use extension visit count instead of historical visit count
                groups[category].totalVisits += (bookmark.extensionVisitCount || 0);

                // Update first/last visited times
                if (bookmark.lastAccessed < groups[category].firstVisited) {
                    groups[category].firstVisited = bookmark.lastAccessed;
                }
                if (bookmark.lastAccessed > groups[category].lastVisited) {
                    groups[category].lastVisited = bookmark.lastAccessed;
                }

                // Update first/last bookmarked times
                if (bookmark.bookmarkedAt < groups[category].firstBookmarked) {
                    groups[category].firstBookmarked = bookmark.bookmarkedAt;
                }
                if (bookmark.bookmarkedAt > groups[category].lastBookmarked) {
                    groups[category].lastBookmarked = bookmark.bookmarkedAt;
                }
            }
        }
    }

    // Handle edge cases for empty groups
    for (const group of Object.values(groups)) {
        if (group.firstVisited === Infinity) {
            group.firstVisited = 0;
        }
        if (group.firstBookmarked === Infinity) {
            group.firstBookmarked = 0;
        }
    }

    return groups;
}

// Group view rendering functions
function renderGroupNavigation() {
    const groups = aggregateBookmarksByGroup(allBookmarks);
    const sortedGroups = Object.values(groups).sort((a, b) => a.name.localeCompare(b.name));

    if (sortedGroups.length === 0) {
        bookmarksContainer.innerHTML = '<div class="no-bookmarks"><h2>No groups found</h2><p>Start by bookmarking some tabs using the extension popup!</p></div>';
        return;
    }

    const html = `
        <div class="group-navigation">
            <div class="group-navigation-header">
                <h2>Browse by Group</h2>
                <p>Click on any group to view all bookmarks in that category</p>
            </div>
            <div class="group-cards">
                ${sortedGroups.map(group => renderGroupCard(group)).join('')}
            </div>
        </div>
    `;

    bookmarksContainer.innerHTML = html;
    setupAllEventListeners();
}

function renderGroupCard(group: GroupStats): string {
    const firstVisitedDate = group.firstVisited > 0 ? new Date(group.firstVisited).toLocaleDateString() : 'Never';
    const lastVisitedDate = group.lastVisited > 0 ? new Date(group.lastVisited).toLocaleDateString() : 'Never';

    return `
        <div class="group-card" data-group-name="${escapeHtml(group.name)}">
            <div class="group-card-content">
                <div class="group-card-header">
                    <h3 class="group-card-title">${escapeHtml(group.name)}</h3>
                    <div class="group-card-count">${group.totalBookmarks}</div>
                </div>
                <div class="group-card-stats">
                    <div class="group-stat">
                        <span class="stat-label">Total Visits:</span>
                        <span class="stat-value">${group.totalVisits}</span>
                    </div>
                    <div class="group-stat">
                        <span class="stat-label">First Visited:</span>
                        <span class="stat-value">${firstVisitedDate}</span>
                    </div>
                    <div class="group-stat">
                        <span class="stat-label">Last Visited:</span>
                        <span class="stat-value">${lastVisitedDate}</span>
                    </div>
                </div>
            </div>
            <div class="group-card-actions">
                <button class="group-action-btn primary view-details-btn" title="View all bookmarks in this group">
                    📋 View Details
                </button>
                <button class="group-action-btn secondary open-all-btn" title="Open all URLs in this group">
                    🚀 Open All (${group.totalBookmarks})
                </button>
            </div>
        </div>
    `;
}

// Event listener setup functions
function setupAllEventListeners() {
    // Only setup once to prevent duplicate listeners
    if (eventListenersSetup) {
        return;
    }

    // Use event delegation on the bookmarks container for all interactions
    bookmarksContainer.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        e.stopPropagation(); // Prevent event bubbling

        // Handle group card content clicks
        if (target.closest('.group-card-content') && !target.closest('.group-card-actions')) {
            const groupCard = target.closest('.group-card') as HTMLElement;
            const groupName = groupCard?.dataset.groupName;
            if (groupName) {
                showGroupDetail(groupName);
            }
            return;
        }

        // Handle view details button clicks
        if (target.classList.contains('view-details-btn')) {
            e.preventDefault();
            const groupCard = target.closest('.group-card') as HTMLElement;
            const groupName = groupCard?.dataset.groupName;
            if (groupName) {
                showGroupDetail(groupName);
            }
            return;
        }

        // Handle open all button clicks (both group card and group detail)
        if (target.classList.contains('open-all-btn') || target.classList.contains('group-detail-open-all')) {
            e.preventDefault();
            let groupName: string | undefined;

            if (target.closest('.group-card')) {
                const groupCard = target.closest('.group-card') as HTMLElement;
                groupName = groupCard?.dataset.groupName;
            } else if (target.closest('.group-detail')) {
                const groupDetail = target.closest('.group-detail') as HTMLElement;
                groupName = groupDetail?.dataset.groupName;
            }

            if (groupName) {
                openAllGroupUrls(groupName);
            }
            return;
        }

        // Handle back button clicks
        if (target.classList.contains('back-btn')) {
            e.preventDefault();
            renderGroupNavigation();
            return;
        }

        // Handle bookmark action buttons
        if (target.classList.contains('open-tab-btn')) {
            e.preventDefault();
            const url = target.dataset.url;
            if (url) {
                chrome.tabs.create({ url: url });
                // The visit will be automatically tracked by the background script
            }
            return;
        }

        if (target.classList.contains('copy-url-btn')) {
            e.preventDefault();
            const url = target.dataset.url;
            if (url) {
                navigator.clipboard.writeText(url).then(() => {
                    const originalText = target.textContent;
                    target.textContent = '✓';
                    setTimeout(() => {
                        target.textContent = originalText;
                    }, 1000);
                }).catch(err => {
                    console.error('Failed to copy URL:', err);
                });
            }
            return;
        }
    });

    eventListenersSetup = true;
}



function openAllGroupUrls(groupName: string) {
    const groups = aggregateBookmarksByGroup(allBookmarks);
    const group = groups[groupName];

    if (!group) {
        console.error('Group not found:', groupName);
        return;
    }

    // Confirm with user before opening many tabs
    const confirmMessage = `This will open ${group.totalBookmarks} tabs. Are you sure you want to continue?`;
    if (group.totalBookmarks > 5 && !confirm(confirmMessage)) {
        return;
    }

    // Open all URLs in the group
    group.bookmarks.forEach(bookmark => {
        chrome.tabs.create({ url: bookmark.url, active: false });
        // Each visit will be automatically tracked by the background script
    });

    // Show success message
    showTemporaryMessage(`✅ Opened ${group.totalBookmarks} tabs from "${groupName}" group`);
}

function showTemporaryMessage(message: string) {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1002;
        font-size: 1rem;
        font-weight: 500;
        max-width: 300px;
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Remove the message after 3 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

function showGroupDetail(groupName: string) {
    const groups = aggregateBookmarksByGroup(allBookmarks);
    const group = groups[groupName];

    if (!group) {
        console.error('Group not found:', groupName);
        return;
    }

    // Sort bookmarks by last accessed (most recent first)
    const sortedBookmarks = group.bookmarks.sort((a, b) => b.lastAccessed - a.lastAccessed);

    const html = `
        <div class="group-detail" data-group-name="${escapeHtml(group.name)}">
            <div class="group-detail-header">
                <div class="group-detail-nav">
                    <button class="back-btn">← Back to Groups</button>
                    <button class="open-all-btn group-detail-open-all" title="Open all ${group.totalBookmarks} URLs in new tabs">
                        🚀 Open All URLs (${group.totalBookmarks})
                    </button>
                </div>
                <div class="group-detail-info">
                    <h2>${escapeHtml(group.name)} - All Bookmarks</h2>
                    <div class="group-detail-stats">
                        <span class="detail-stat">
                            <strong>${group.totalBookmarks}</strong> bookmarks
                        </span>
                        <span class="detail-stat">
                            <strong>${group.totalVisits}</strong> total visits
                        </span>
                        <span class="detail-stat">
                            First visited: <strong>${group.firstVisited > 0 ? new Date(group.firstVisited).toLocaleDateString() : 'Never'}</strong>
                        </span>
                        <span class="detail-stat">
                            Last visited: <strong>${group.lastVisited > 0 ? new Date(group.lastVisited).toLocaleDateString() : 'Never'}</strong>
                        </span>
                    </div>
                </div>
            </div>
            <div class="group-detail-bookmarks">
                ${sortedBookmarks.map(bookmark => renderGroupBookmarkItem(bookmark)).join('')}
            </div>
        </div>
    `;

    bookmarksContainer.innerHTML = html;
    setupAllEventListeners();
}

function renderGroupBookmarkItem(bookmark: GroupedBookmark): string {
    const favicon = bookmark.favIconUrl
        ? `<img src="${bookmark.favIconUrl}" alt="" class="bookmark-favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`
        : '';

    const defaultFavicon = `<div class="bookmark-favicon default" ${bookmark.favIconUrl ? 'style="display:none;"' : ''}>${bookmark.title.charAt(0).toUpperCase()}</div>`;

    const lastAccessed = new Date(bookmark.lastAccessed).toLocaleDateString();
    const bookmarkedAt = new Date(bookmark.bookmarkedAt).toLocaleDateString();

    return `
        <div class="bookmark-item group-bookmark-item">
            ${favicon}
            ${defaultFavicon}
            <div class="bookmark-content">
                <a href="${bookmark.url}" class="bookmark-title" target="_blank" rel="noopener noreferrer">
                    ${escapeHtml(bookmark.title)}
                </a>
                <div class="bookmark-url">${escapeHtml(bookmark.url)}</div>
                ${bookmark.description ? `<div class="bookmark-description">${escapeHtml(bookmark.description)}</div>` : ''}
                <div class="bookmark-meta">
                    <span>Extension Visits: ${bookmark.extensionVisitCount || 0}</span>
                    <span>Historical Visits: ${bookmark.visitCount}</span>
                    <span>Last accessed: ${lastAccessed}</span>
                    <span>Bookmarked: ${bookmarkedAt}</span>
                    <span>Session: ${bookmark.sessionDate}</span>
                </div>
            </div>
            <div class="bookmark-actions">
                <button class="action-btn open-tab-btn" data-url="${escapeHtml(bookmark.url)}" title="Open in new tab">
                    🔗
                </button>
                <button class="action-btn copy-url-btn" data-url="${escapeHtml(bookmark.url)}" title="Copy URL">
                    📋
                </button>
            </div>
        </div>
    `;
}

// Modal functions
function showJsonModal() {
    const formattedJson = JSON.stringify(allBookmarks, null, 2);
    jsonContent.textContent = formattedJson;
    jsonModal.style.display = 'block';
}

function hideJsonModal() {
    jsonModal.style.display = 'none';
}

function copyJsonToClipboard() {
    const jsonText = jsonContent.textContent || '';
    navigator.clipboard.writeText(jsonText).then(() => {
        const originalText = copyJsonBtn.textContent;
        copyJsonBtn.textContent = '✓ Copied!';
        setTimeout(() => {
            copyJsonBtn.textContent = originalText;
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy JSON:', err);
        copyJsonBtn.textContent = '❌ Failed';
        setTimeout(() => {
            copyJsonBtn.textContent = 'Copy to Clipboard';
        }, 2000);
    });
}

// Export functions
function exportAsJson() {
    const dataStr = JSON.stringify(allBookmarks, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `booksmart-bookmarks-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportForChrome() {
    // Convert BookSmart format to Chrome bookmarks format
    const chromeBookmarks = convertToChromeFormat(allBookmarks);
    const dataStr = JSON.stringify(chromeBookmarks, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `chrome-bookmarks-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportAsCsv() {
    const csvData = convertToCsvFormat(allBookmarks);
    const dataBlob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `booksmart-bookmarks-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function convertToChromeFormat(bookmarks: AllBookmarks) {
    const chromeFormat = {
        version: 1,
        roots: {
            bookmark_bar: {
                children: [] as any[],
                date_added: Date.now().toString(),
                date_modified: Date.now().toString(),
                id: "1",
                name: "Bookmarks bar",
                type: "folder"
            },
            other: {
                children: [] as any[],
                date_added: Date.now().toString(),
                date_modified: Date.now().toString(),
                id: "2",
                name: "Other bookmarks",
                type: "folder"
            },
            synced: {
                children: [] as any[],
                date_added: Date.now().toString(),
                date_modified: Date.now().toString(),
                id: "3",
                name: "Mobile bookmarks",
                type: "folder"
            }
        }
    };

    let idCounter = 4;

    // Process each session
    for (const [sessionKey, session] of Object.entries(bookmarks)) {
        const sessionDate = new Date(parseInt(sessionKey.replace('bookmarks_', ''))).toLocaleDateString();

        // Create a folder for this session
        const sessionFolder = {
            children: [] as any[],
            date_added: sessionKey.replace('bookmarks_', ''),
            date_modified: sessionKey.replace('bookmarks_', ''),
            id: (idCounter++).toString(),
            name: `BookSmart Session - ${sessionDate}`,
            type: "folder"
        };

        // Process each category in the session
        for (const [category, categoryBookmarks] of Object.entries(session)) {
            const categoryFolder = {
                children: [] as any[],
                date_added: sessionKey.replace('bookmarks_', ''),
                date_modified: sessionKey.replace('bookmarks_', ''),
                id: (idCounter++).toString(),
                name: category,
                type: "folder"
            };

            // Add bookmarks to category folder
            for (const bookmark of categoryBookmarks) {
                categoryFolder.children.push({
                    date_added: bookmark.bookmarkedAt.toString(),
                    id: (idCounter++).toString(),
                    name: bookmark.title,
                    type: "url",
                    url: bookmark.url
                });
            }

            sessionFolder.children.push(categoryFolder);
        }

        chromeFormat.roots.bookmark_bar.children.push(sessionFolder);
    }

    return chromeFormat;
}

function convertToCsvFormat(bookmarks: AllBookmarks): string {
    const csvRows = [];

    // CSV Header
    csvRows.push('Title,URL,Description,Category,Session Date,Bookmarked Date,Last Accessed,Visit Count,Favicon URL');

    // Process each session
    for (const [sessionKey, session] of Object.entries(bookmarks)) {
        const sessionDate = new Date(parseInt(sessionKey.replace('bookmarks_', ''))).toLocaleDateString();

        // Process each category in the session
        for (const [category, categoryBookmarks] of Object.entries(session)) {
            // Add bookmarks to CSV
            for (const bookmark of categoryBookmarks) {
                const row = [
                    escapeCSV(bookmark.title),
                    escapeCSV(bookmark.url),
                    escapeCSV(bookmark.description),
                    escapeCSV(category),
                    escapeCSV(sessionDate),
                    escapeCSV(new Date(bookmark.bookmarkedAt).toLocaleDateString()),
                    escapeCSV(new Date(bookmark.lastAccessed).toLocaleDateString()),
                    bookmark.visitCount.toString(),
                    escapeCSV(bookmark.favIconUrl || '')
                ];
                csvRows.push(row.join(','));
            }
        }
    }

    return csvRows.join('\n');
}

function escapeCSV(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
}

// Reset functionality
function showResetConfirmation() {
    confirmModal.style.display = 'block';
}

function hideResetConfirmation() {
    confirmModal.style.display = 'none';
}

async function resetAllBookmarks() {
    try {
        // Show loading state
        confirmResetBtn.textContent = 'Deleting...';
        confirmResetBtn.disabled = true;

        // Get all storage keys
        const allData = await chrome.storage.local.get(null);
        const keysToRemove: string[] = [];

        // Find all bookmark-related keys
        for (const key of Object.keys(allData)) {
            if (key.startsWith('bookmarks_') || key === 'url_index') {
                keysToRemove.push(key);
            }
        }

        // Remove all bookmark data
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log(`Removed ${keysToRemove.length} bookmark-related storage keys`);
        }

        // Reset global variables
        allBookmarks = {};
        filteredBookmarks = {};
        currentFilter = 'all';

        // Hide confirmation modal
        hideResetConfirmation();

        // Show success message and refresh UI
        showResetSuccessMessage();

        // Refresh the page content
        await refreshBookmarksView();

    } catch (error) {
        console.error('Error resetting bookmarks:', error);
        alert('Error occurred while resetting bookmarks. Please try again.');
    } finally {
        // Reset button state
        confirmResetBtn.textContent = 'Yes, Delete All';
        confirmResetBtn.disabled = false;
    }
}

function showResetSuccessMessage() {
    // Create a temporary success message
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1002;
        font-size: 1rem;
        font-weight: 500;
    `;
    successDiv.textContent = '✅ All bookmarks have been reset successfully!';

    document.body.appendChild(successDiv);

    // Remove the message after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

async function refreshBookmarksView() {
    // Reset the loading state
    loadingDiv.style.display = 'block';
    bookmarksContainer.innerHTML = '';
    statsContainer.innerHTML = '';

    // Clear filter buttons (keep only "All")
    filterButtons.innerHTML = '<button class="filter-btn active" data-category="all">All</button>';

    // Show the no bookmarks message
    setTimeout(() => {
        loadingDiv.style.display = 'none';
        noBookmarksDiv.style.display = 'block';
    }, 500);
}

// Keep minimal global functions for backward compatibility if needed
(window as any).showGroupDetail = function(groupName: string) {
    showGroupDetail(groupName);
};

(window as any).renderGroupNavigation = function() {
    renderGroupNavigation();
};
