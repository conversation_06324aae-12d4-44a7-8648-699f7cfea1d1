<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookSmart Settings</title>
    <style>
        :root {
            /* Light theme colors */
            --bg-primary: #f5f5f5;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f8f9fa;
            --bg-quaternary: #e9ecef;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --border-primary: #e0e0e0;
            --border-secondary: #f0f0f0;
            --accent-primary: #007BFF;
            --accent-secondary: #00BFFF;
            --accent-success: #28a745;
            --accent-success-secondary: #20c997;
            --accent-danger: #dc3545;
            --accent-warning: #ffc107;
            --accent-info: #17a2b8;
            --accent-gray: #6c757d;
            --shadow-light: rgba(0,0,0,0.1);
            --shadow-medium: rgba(0,0,0,0.15);
            --gradient-primary: linear-gradient(135deg, #007BFF, #00BFFF);
            --gradient-success: linear-gradient(45deg, #28a745, #20c997);
            --gradient-header: linear-gradient(135deg, #f8f9fa, #e9ecef);
            --gradient-header-dark: linear-gradient(135deg, #2c2c2c, #404040);
        }

        [data-theme="dark"] {
            /* Dark theme colors */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --bg-quaternary: #4a4a4a;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-tertiary: #999999;
            --border-primary: #404040;
            --border-secondary: #505050;
            --accent-primary: #4dabf7;
            --accent-secondary: #74c0fc;
            --accent-success: #51cf66;
            --accent-success-secondary: #69db7c;
            --accent-danger: #ff6b6b;
            --accent-warning: #ffd43b;
            --accent-info: #22b8cf;
            --accent-gray: #868e96;
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.4);
            --gradient-primary: linear-gradient(135deg, #4dabf7, #74c0fc);
            --gradient-success: linear-gradient(45deg, #51cf66, #69db7c);
            --gradient-header: linear-gradient(135deg, #3a3a3a, #4a4a4a);
            --gradient-header-dark: linear-gradient(135deg, #2c2c2c, #404040);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px var(--shadow-light);
            position: relative;
        }

        [data-theme="dark"] .header {
            background: var(--gradient-header-dark);
            color: var(--text-primary);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .header-nav {
            position: absolute;
            top: 1rem;
            left: 2rem;
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .settings-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: var(--gradient-header);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-primary);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .section-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .section-content {
            padding: 2rem;
        }

        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .model-card {
            border: 2px solid var(--border-primary);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .model-card:hover {
            border-color: var(--accent-primary);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .model-card.selected {
            border-color: var(--accent-success);
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(32, 201, 151, 0.05));
        }

        .model-card.downloading {
            border-color: var(--accent-warning);
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.1));
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .model-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .model-size {
            font-size: 0.85rem;
            color: var(--text-tertiary);
            background: var(--bg-tertiary);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .model-specs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
        }

        .spec-label {
            color: var(--text-secondary);
        }

        .spec-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .model-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .model-actions {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .model-btn, .reset-btn {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 100px;
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--accent-success);
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--accent-danger);
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: var(--bg-quaternary);
            color: var(--text-primary);
        }

        .export-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
            background: var(--accent-primary);
            color: white;
        }

        .export-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .export-btn.secondary {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }

        .export-btn.secondary:hover {
            background: var(--bg-quaternary);
            border-color: var(--accent-primary);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
        }

        .modal-content {
            background-color: var(--bg-secondary);
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px var(--shadow-medium);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .close-btn:hover {
            background-color: rgba(255,255,255,0.2);
        }

        .json-viewer {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
            margin: 1.5rem;
            color: var(--text-primary);
        }

        .copy-json-btn {
            margin: 0 1.5rem 1.5rem 1.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s ease;
        }

        .copy-json-btn:hover {
            background: #0056b3;
        }

        .progress-container {
            margin-top: 1rem;
            display: none;
        }

        .progress-container.visible {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-success);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.85rem;
            color: var(--text-secondary);
            text-align: center;
        }

        .recommended-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .tag {
            background: var(--accent-primary);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-current {
            background: var(--accent-success);
            color: white;
        }

        .status-downloaded {
            background: var(--accent-info);
            color: white;
        }

        .status-downloading {
            background: var(--accent-warning);
            color: white;
        }

        .storage-info {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .storage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .storage-stat {
            text-align: center;
        }

        .storage-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--accent-primary);
            margin-bottom: 0.25rem;
        }

        .storage-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .cleanup-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1.5rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: var(--text-secondary);
        }

        .error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid var(--accent-danger);
            color: var(--accent-danger);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .reset-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-primary);
            display: flex;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .model-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .header-nav {
                position: static;
                justify-content: center;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-nav">
            <a href="bookmarks.html" class="nav-btn">📚 Bookmarks</a>
            <a href="popup.html" class="nav-btn">🏠 Home</a>
        </div>
        <h1>⚙️ BookSmart Settings</h1>
        <p>Manage your AI models and preferences</p>
    </div>

    <div class="container">
        <!-- Storage Overview Section -->
        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">💾 Storage Overview</div>
                <div class="section-description">Monitor your model storage usage and manage cache</div>
            </div>
            <div class="section-content">
                <div class="storage-info">
                    <div class="storage-stats">
                        <div class="storage-stat">
                            <div class="storage-value" id="total-storage">-</div>
                            <div class="storage-label">Total Used</div>
                        </div>
                        <div class="storage-stat">
                            <div class="storage-value" id="models-count">-</div>
                            <div class="storage-label">Models Downloaded</div>
                        </div>
                        <div class="storage-stat">
                            <div class="storage-value" id="current-model">-</div>
                            <div class="storage-label">Current Model</div>
                        </div>
                    </div>
                    <div class="cleanup-actions">
                        <button class="model-btn btn-secondary" id="refresh-storage">🔄 Refresh</button>
                        <button class="model-btn btn-danger" id="clear-cache">🗑️ Clear All Cache</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Selection Section -->
        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">🤖 AI Model Selection</div>
                <div class="section-description">Choose and manage the AI model for bookmark classification</div>
            </div>
            <div class="section-content">
                <div class="loading" id="models-loading">Loading available models...</div>
                <div class="error" id="models-error" style="display: none;"></div>
                <div class="model-grid" id="models-grid" style="display: none;"></div>
            </div>
        </div>

        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">📤 Export Bookmarks</div>
                <div class="section-description">Export your bookmarks in various formats</div>
            </div>
            <div class="section-content">
                <div class="export-buttons">
                    <button class="export-btn" id="view-json-btn">📄 View JSON</button>
                    <button class="export-btn" id="export-json-btn">💾 Export JSON</button>
                    <button class="export-btn secondary" id="export-chrome-btn">🌐 Export for Chrome</button>
                    <button class="export-btn secondary" id="export-csv-btn">📊 Export CSV</button>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">⛔️ Danger Section</div>
                <div class="section-description">Manage your bookmarks</div>
            </div>
            <div class="section-content">
                <button class="btn-danger reset-btn" id="reset-bookmarks-btn">🗑️ Delete All Bookmarks</button>
            </div>
        </div>
    </div>

    <!-- JSON Viewer Modal -->
    <div id="json-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Raw JSON Data</h2>
                <button class="close-btn" id="close-modal">&times;</button>
            </div>
            <div class="json-viewer" id="json-content">
                <!-- JSON content will be populated by JavaScript -->
            </div>
            <button class="copy-json-btn" id="copy-json-btn">Copy to Clipboard</button>
        </div>
    </div>

    <script src="js/settings.js"></script>
</body>
</html>
