{"manifest_version": 3, "name": "BookSmart", "version": "1.0.0", "description": "Uses WebLLM to read all open tabs and group them into smart bookmarks.", "permissions": ["tabs", "storage", "history", "scripting"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "js/background.js"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}}