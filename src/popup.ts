/**
 * popup.ts
 *
 * This script handles the logic for the extension's popup window.
 * It sends a message to the background script to initiate the bookmarking process.
 */

const bookmarkBtn = document.getElementById('bookmark-all-btn') as HTMLButtonElement;
const viewBookmarksBtn = document.getElementById('view-bookmarks-btn') as HTMLButtonElement;
const settingsBtn = document.getElementById('settings-btn') as HTMLButtonElement;
const statusDiv = document.getElementById('status') as HTMLDivElement;

// Initialize theme on popup load
document.addEventListener('DOMContentLoaded', async () => {
    await initializePopupTheme();
});

// Theme management for popup
async function initializePopupTheme() {
    try {
        // Load saved theme preference
        const result = await chrome.storage.local.get(['theme']);
        const savedTheme = result.theme as 'light' | 'dark' | 'system' || 'system';

        applyPopupTheme(savedTheme);

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', () => {
                if (savedTheme === 'system') {
                    applyPopupTheme('system');
                }
            });
        }
    } catch (error) {
        console.error('Error initializing popup theme:', error);
        // Fallback to system theme
        applyPopupTheme('system');
    }
}

function applyPopupTheme(theme: 'light' | 'dark' | 'system') {
    const body = document.body;

    // Remove existing theme attributes
    body.removeAttribute('data-theme');

    if (theme === 'dark') {
        body.setAttribute('data-theme', 'dark');
    } else if (theme === 'light') {
        // Light theme is the default, no attribute needed
    } else if (theme === 'system') {
        // Use system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.setAttribute('data-theme', 'dark');
        }
    }
}

bookmarkBtn?.addEventListener('click', () => {
    statusDiv.textContent = 'Processing...';
    bookmarkBtn.disabled = true;

    // Send a message to the background script to start the main process.
    // The background script has more permissions and a longer-running context.
    chrome.runtime.sendMessage({ action: "bookmarkAllTabs" }, (response) => {
        if (chrome.runtime.lastError) {
            console.error(chrome.runtime.lastError.message);
            statusDiv.textContent = 'Error occurred.';
            bookmarkBtn.disabled = false;
        } else {
            console.log(response);

            // Provide detailed feedback based on the response
            if (response.status === "Processing complete") {
                if (response.duplicates > 0) {
                    statusDiv.innerHTML = `✅ Saved ${response.newBookmarks} new bookmarks<br>⚠️ Skipped ${response.duplicates} duplicates`;
                } else {
                    statusDiv.textContent = `✅ Saved ${response.newBookmarks} bookmarks!`;
                }
            } else if (response.status === "No new bookmarks") {
                statusDiv.innerHTML = `ℹ️ All tabs already bookmarked<br>Skipped ${response.duplicates} duplicates`;
            } else if (response.status === "No valid tabs") {
                statusDiv.textContent = 'ℹ️ No valid tabs to bookmark';
            } else {
                statusDiv.textContent = response.message || 'Tabs processed!';
            }

            // The popup will close automatically after a longer delay to show the message
            setTimeout(() => window.close(), 3000);
        }
        bookmarkBtn.disabled = false;
    });
});

viewBookmarksBtn?.addEventListener('click', () => {
    // Open the bookmarks viewer in a new tab
    chrome.tabs.create({ url: chrome.runtime.getURL('bookmarks.html') });
    window.close();
});

settingsBtn?.addEventListener('click', () => {
    // Open the settings page in a new tab
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
    window.close();
});
